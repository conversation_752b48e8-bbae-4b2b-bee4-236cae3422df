Vous êtes un développeur Flutter expert. Créez une application mobile professionnelle nommée ClockIn, conçue pour le pointage sécurisé des employés sur des chantiers, avec un design moderne, épuré et visuellement impressionnant (effet "wow") qui reflète le domaine de la sécurité. L'application doit être disponible en arabe et en français, avec une prise en charge complète de la localisation (RTL pour l'arabe, LTR pour le français). Elle doit inclure des interfaces pour les employés et les administrateurs, avec les fonctionnalités suivantes :

Côté Employé :
Écran de connexion (Login) :
Interface sécurisée avec des champs pour l'email/identifiant et le mot de passe.
Authentification robuste (par exemple, avec Firebase Authentication ou une API sécurisée).
Support multilingue (arabe/français) avec un sélecteur de langue.
Design professionnel avec des animations fluides (par exemple, transitions lors de la connexion).
Écran de déconnexion (Logout) :
Bouton de déconnexion sécurisé qui réinitialise la session et redirige vers l'écran de connexion.
Confirmation de déconnexion avec une boîte de dialogue élégante.
Interface de pointage :
Un écran principal avec un bouton de pointage (design moderne avec effet de clic animé).
Détection de géolocalisation :
L'application utilise le plugin geolocator pour vérifier l'emplacement de l'employé en temps réel.
Le bouton de pointage n'est actif que si l'employé se trouve dans un rayon de 50 mètres de l'emplacement du chantier défini par l'admin.
Si l'employé est hors du rayon, afficher un message d'erreur (par exemple, "Vous devez être à proximité du chantier pour pointer").
Mécanisme de pointage :
Lorsqu'un employé clique sur le bouton de pointage, un timer démarre si la géolocalisation est validée.
Le timer s'arrête lorsque l'employé clique à nouveau sur le bouton pour terminer le pointage.
Enregistrer les informations suivantes :
Date et heure de début du pointage.
Date et heure de fin du pointage.
Durée totale du pointage.
Emplacement géographique (latitude/longitude) au moment du pointage.
Sécurité anti-trafic :
Implémenter des mécanismes pour empêcher la falsification du pointage, comme :
Vérification continue de la géolocalisation pendant le pointage.
Désactivation des fonctionnalités si la localisation GPS est désactivée.
Utilisation d'une signature numérique ou d'un hachage pour valider les données envoyées.
Enregistrement des données sur un serveur sécurisé (par exemple, via une API REST avec chiffrement HTTPS).
Les informations de pointage sont envoyées à l'admin en temps réel ou par lots, selon la connectivité.
Notifications :
Si l'admin demande une vérification du pointage, l'employé reçoit une notification push (via Firebase Cloud Messaging ou similaire).
L'employé doit alors confirmer sa géolocalisation en cliquant sur un bouton dans l'application, qui envoie sa position actuelle à l'admin.
Côté Admin :
Tableau de bord (Dashboard) :
Un tableau de bord moderne avec un design visuellement attrayant (graphiques, animations, couleurs sobres mais professionnelles).
Support multilingue (arabe/français) avec prise en charge RTL/LTR.
Gestion des géolocalisations des chantiers :
Un espace pour ajouter, modifier ou supprimer des emplacements de chantiers (latitude, longitude, rayon de 50 mètres).
Interface intuitive avec une carte interactive (par exemple, utilisant google_maps_flutter) pour définir les emplacements.
Sauvegarde sécurisée des données sur un serveur (via une API REST).
Historique des pointages :
Un espace pour consulter l'historique des pointages de tous les employés.
Afficher les informations suivantes pour chaque pointage :
Nom de l'employé.
Date/heure de début et de fin.
Durée du pointage.
Emplacement géographique (visualisable sur une carte si possible).
Filtres pour trier par employé, date ou chantier.
Possibilité d'exporter l'historique (par exemple, en CSV ou PDF).
Gestion des employés (CRUD) :
Interface pour effectuer les opérations CRUD (Create, Read, Update, Delete) sur les employés :
Créer : Ajouter un nouvel employé avec nom, email, rôle, etc.
Lire : Afficher la liste des employés avec leurs détails.
Mettre à jour : Modifier les informations d'un employé.
Supprimer : Supprimer un employé (avec confirmation).
Interface claire avec une table ou une liste paginée, avec recherche et tri.
Vérification des pointages :
L'admin peut envoyer une demande de vérification de pointage à un employé spécifique.
Une notification push est envoyée à l'employé, qui doit confirmer sa position.
Les résultats de la vérification (emplacement, date/heure) sont enregistrés et visibles dans le tableau de bord.
Exigences techniques :
Framework : Utiliser Flutter pour le développement multiplateforme (iOS et Android).
Design :
Créer un design moderne avec un effet "wow" : utiliser des animations fluides (par exemple, avec flare_flutter ou lottie), des couleurs professionnelles (bleu, gris, blanc avec accents dynamiques), et des icônes modernes (par exemple, font_awesome_flutter).
Respecter les standards de sécurité visuelle (contrastes élevés, lisibilité en arabe/français).
Backend :
Utiliser une API REST sécurisée (par exemple, avec Node.js, Django, ou Laravel) pour gérer les données (pointages, employés, chantiers).
Stocker les données dans une base de données (par exemple, Firebase Firestore, PostgreSQL, ou MongoDB).
Implémenter un chiffrement des données sensibles (HTTPS, JWT pour l'authentification).
Localisation :
Intégrer un package comme flutter_localizations pour gérer le support arabe (RTL) et français (LTR).
Permettre à l'utilisateur de basculer entre les langues depuis l'application.
Sécurité :
Authentification sécurisée (Firebase Auth ou JWT).
Vérification continue de la géolocalisation pour éviter les fraudes.
Sauvegarde des données sur un serveur sécurisé avec validation des entrées.
Plugins suggérés :
geolocator pour la géolocalisation.
google_maps_flutter pour les cartes interactives.
firebase_auth pour l'authentification.
firebase_messaging pour les notifications push.
http pour les appels API.
provider ou riverpod pour la gestion d'état.
Livrables :
Code source Flutter complet, organisé selon une architecture propre (par exemple, Clean Architecture ou MVC).
Documentation expliquant l'installation, la configuration du backend, et les tests.
Interface utilisateur avec un design professionnel et des animations fluides.
Tests unitaires pour les fonctionnalités critiques (pointage, géolocalisation, authentification).
Instructions pour déployer l'application sur iOS et Android.import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        // This is the theme of your application.
        //
        // TRY THIS: Try running your application with "flutter run". You'll see
        // the application has a purple toolbar. Then, without quitting the app,
        // try changing the seedColor in the colorScheme below to Colors.green
        // and then invoke "hot reload" (save your changes or press the "hot
        // reload" button in a Flutter-supported IDE, or press "r" if you used
        // the command line to start the app).
        //
        // Notice that the counter didn't reset back to zero; the application
        // state is not lost during the reload. To reset the state, use hot
        // restart instead.
        //
        // This works for code too, not just values: Most code changes can be
        // tested with just a hot reload.
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;

  void _incrementCounter() {
    setState(() {
      // This call to setState tells the Flutter framework that something has
      // changed in this State, which causes it to rerun the build method below
      // so that the display can reflect the updated values. If we changed
      // _counter without calling setState(), then the build method would not be
      // called again, and so nothing would appear to happen.
      _counter++;
    });
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
      appBar: AppBar(
        // TRY THIS: Try changing the color here to a specific color (to
        // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
        // change color while the other colors stay the same.
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        // Here we take the value from the MyHomePage object that was created by
        // the App.build method, and use it to set our appbar title.
        title: Text(widget.title),
      ),
      body: Center(
        // Center is a layout widget. It takes a single child and positions it
        // in the middle of the parent.
        child: Column(
          // Column is also a layout widget. It takes a list of children and
          // arranges them vertically. By default, it sizes itself to fit its
          // children horizontally, and tries to be as tall as its parent.
          //
          // Column has various properties to control how it sizes itself and
          // how it positions its children. Here we use mainAxisAlignment to
          // center the children vertically; the main axis here is the vertical
          // axis because Columns are vertical (the cross axis would be
          // horizontal).
          //
          // TRY THIS: Invoke "debug painting" (choose the "Toggle Debug Paint"
          // action in the IDE, or press "p" in the console), to see the
          // wireframe for each widget.
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text('You have pushed the button this many times:'),
            Text(
              '$_counter',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}
